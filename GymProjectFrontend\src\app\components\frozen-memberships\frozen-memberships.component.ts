import { Component, OnInit } from '@angular/core';
import { MembershipService } from '../../services/membership.service';
import { MembershipFreezeHistoryService } from '../../services/membership-freeze-history.service';
import { ToastrService } from 'ngx-toastr';
import { MembershipFreezeHistory } from '../../models/membershipFreezeHistory';
import { DialogService } from '../../services/dialog.service';

@Component({
  selector: 'app-frozen-memberships',
  templateUrl: './frozen-memberships.component.html',
  styleUrls: ['./frozen-memberships.component.css'],
  standalone: false,
})
export class FrozenMembershipsComponent implements OnInit {
  frozenMemberships: any[] = [];
  freezeHistories: MembershipFreezeHistory[] = [];
  isProcessing: boolean = false;
  isLoading: boolean = false;
  selectedMembershipId: number | null = null;
  showHistoryModal: boolean = false;
  showFrozenMemberships: boolean = true;
  searchText: string = '';
  filteredHistories: MembershipFreezeHistory[] = [];

  // Yeni filtre özellikleri
  selectedStatusFilter: string = '';
  selectedBranchFilter: string = '';
  selectedHistoryTypeFilter: string = '';
  selectedHistoryBranchFilter: string = '';
  filteredMemberships: any[] = [];
  filteredHistoriesData: MembershipFreezeHistory[] = [];

  constructor(
    private membershipService: MembershipService,
    private freezeHistoryService: MembershipFreezeHistoryService,
    private toastr: ToastrService,
    private dialogService: DialogService
  ) {}

  ngOnInit(): void {
    this.loadFrozenMemberships();
  }

  setActiveView(showFrozen: boolean): void {
    this.showFrozenMemberships = showFrozen;
    if (!showFrozen && this.freezeHistories.length === 0) {
      this.loadFreezeHistories();
    }
  }

  toggleView() {
    this.showFrozenMemberships = !this.showFrozenMemberships;
    if (!this.showFrozenMemberships && this.freezeHistories.length === 0) {
      this.loadFreezeHistories();
    }
  }

  loadFrozenMemberships(): void {
    this.isLoading = true;
    this.membershipService.getFrozenMemberships().subscribe({
      next: (response) => {
        if (response.success) {
          this.frozenMemberships = response.data;
          this.filteredMemberships = [...this.frozenMemberships];
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Dondurulmuş üyelikler yüklenirken bir hata oluştu.');
        this.isLoading = false;
      },
    });
  }

  loadFreezeHistories(): void {
    this.isLoading = true;
    this.freezeHistoryService.getFreezeHistories().subscribe({
      next: (response) => {
        if (response.success) {
          this.freezeHistories = response.data;
          this.filteredHistories = this.freezeHistories;
          this.filteredHistoriesData = [...this.freezeHistories];
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Dondurma geçmişi yüklenirken bir hata oluştu.');
        this.isLoading = false;
      },
    });
  }

  filterHistories(searchText: string): void {
    if (!searchText.trim()) {
      this.filteredHistories = this.freezeHistories;
    } else {
      const searchLower = searchText.toLowerCase().trim();
      this.filteredHistories = this.freezeHistories.filter((history) =>
        history.memberName.toLowerCase().includes(searchLower)
      );
    }
  }

  onSearchChange(event: any): void {
    this.searchText = event.target.value;
    if (this.showFrozenMemberships) {
      this.filterMemberships();
    } else {
      this.filterHistories(this.searchText);
    }
  }

  onFilterChange(): void {
    this.filterMemberships();
  }

  onHistoryFilterChange(): void {
    this.filterHistoriesData();
  }

  getRemainingDays(endDate: string): number {
    const end = new Date(endDate);
    const now = new Date();
    const diff = Math.ceil(
      (end.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    );
    return Math.max(0, diff);
  }

  cancelFreeze(membership: any): void {
    this.dialogService
      .confirmFreezeCancel(membership.memberName, 0)
      .subscribe((result) => {
        if (result) {
          this.isProcessing = true;
          this.membershipService
            .cancelFreeze(membership.membershipID)
            .subscribe({
              next: (response) => {
                if (response.success) {
                  this.toastr.success(
                    'Üyelik dondurma işlemi tamamen iptal edildi.'
                  );
                  this.loadFrozenMemberships();
                  if (!this.showFrozenMemberships) {
                    this.loadFreezeHistories();
                  }
                } else {
                  this.toastr.error(response.message);
                }
                this.isProcessing = false;
              },
              error: (error) => {
                this.toastr.error('İşlem sırasında bir hata oluştu.');
                this.isProcessing = false;
              },
            });
        }
      });
  }

  reactivateFromToday(membership: any): void {
    const usedDays = Math.floor(
      (new Date().getTime() - new Date(membership.freezeStartDate).getTime()) /
        (1000 * 60 * 60 * 24)
    );

    this.dialogService
      .confirmReactivateFromToday(membership.memberName, usedDays)
      .subscribe((result) => {
        if (result) {
          this.isProcessing = true;
          this.membershipService
            .reactivateFromToday(membership.membershipID)
            .subscribe({
              next: (response) => {
                if (response.success) {
                  this.toastr.success('Üyelik bugünden itibaren aktif edildi.');
                  this.loadFrozenMemberships();
                  if (!this.showFrozenMemberships) {
                    this.loadFreezeHistories();
                  }
                } else {
                  this.toastr.error(response.message);
                }
                this.isProcessing = false;
              },
              error: (error) => {
                this.toastr.error('İşlem sırasında bir hata oluştu.');
                this.isProcessing = false;
              },
            });
        }
      });
  }
  showHistory(membershipId: number): void {
    this.selectedMembershipId = membershipId;
    this.showHistoryModal = true;
  }

  closeHistoryModal(): void {
    this.showHistoryModal = false;
    this.selectedMembershipId = null;
  }

  getFilteredHistory(): MembershipFreezeHistory[] {
    if (!this.selectedMembershipId) return [];
    return this.freezeHistories
      .filter(
        (h) =>
          h.memberName ===
          this.frozenMemberships.find(
            (m) => m.membershipID === this.selectedMembershipId
          )?.memberName
      )
      .sort(
        (a, b) =>
          new Date(b.creationDate).getTime() -
          new Date(a.creationDate).getTime()
      );
  }

  formatDate(date: string | Date): string {
    return new Date(date).toLocaleDateString('tr-TR');
  }

  calculateUsedDays(history: MembershipFreezeHistory): number {
    if (!history.actualEndDate) return 0;
    const start = new Date(history.startDate);
    const end = new Date(history.actualEndDate);
    return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
  }

  // Yeni metodlar
  filterMemberships(): void {
    let filtered = [...this.frozenMemberships];

    // Arama filtresi
    if (this.searchText.trim()) {
      const searchLower = this.searchText.toLowerCase().trim();
      filtered = filtered.filter(membership =>
        membership.memberName.toLowerCase().includes(searchLower) ||
        membership.phoneNumber.includes(searchLower)
      );
    }

    // Durum filtresi
    if (this.selectedStatusFilter) {
      filtered = filtered.filter(membership => {
        const remainingDays = this.getRemainingDays(membership.freezeEndDate);
        switch (this.selectedStatusFilter) {
          case 'critical':
            return remainingDays <= 3;
          case 'warning':
            return remainingDays > 3 && remainingDays <= 10;
          case 'safe':
            return remainingDays > 10;
          default:
            return true;
        }
      });
    }

    // Branş filtresi
    if (this.selectedBranchFilter) {
      filtered = filtered.filter(membership => membership.branch === this.selectedBranchFilter);
    }

    this.filteredMemberships = filtered;
  }

  filterHistoriesData(): void {
    let filtered = [...this.freezeHistories];

    // Arama filtresi
    if (this.searchText.trim()) {
      const searchLower = this.searchText.toLowerCase().trim();
      filtered = filtered.filter(history =>
        history.memberName.toLowerCase().includes(searchLower)
      );
    }

    // İşlem türü filtresi
    if (this.selectedHistoryTypeFilter) {
      if (this.selectedHistoryTypeFilter === 'Tamamlanan') {
        filtered = filtered.filter(history => !history.cancellationType);
      } else {
        filtered = filtered.filter(history => history.cancellationType === this.selectedHistoryTypeFilter);
      }
    }

    // Branş filtresi
    if (this.selectedHistoryBranchFilter) {
      filtered = filtered.filter(history => history.branch === this.selectedHistoryBranchFilter);
    }

    this.filteredHistoriesData = filtered;
  }

  getFilteredMemberships(): any[] {
    return this.filteredMemberships;
  }

  getFilteredHistories(): MembershipFreezeHistory[] {
    return this.filteredHistoriesData;
  }

  // İstatistik metodları
  getCriticalCount(): number {
    return this.frozenMemberships.filter(membership =>
      this.getRemainingDays(membership.freezeEndDate) <= 3
    ).length;
  }

  getWarningCount(): number {
    return this.frozenMemberships.filter(membership => {
      const days = this.getRemainingDays(membership.freezeEndDate);
      return days > 3 && days <= 10;
    }).length;
  }

  getSafeCount(): number {
    return this.frozenMemberships.filter(membership =>
      this.getRemainingDays(membership.freezeEndDate) > 10
    ).length;
  }

  // Yardımcı metodlar
  getUniqueBranches(): string[] {
    const branches = this.frozenMemberships.map(m => m.branch);
    return [...new Set(branches)].sort();
  }

  getHistoryUniqueBranches(): string[] {
    const branches = this.freezeHistories.map(h => h.branch);
    return [...new Set(branches)].sort();
  }

  trackByMembershipId(index: number, membership: any): any {
    return membership.membershipID;
  }

  trackByHistoryId(index: number, history: MembershipFreezeHistory): any {
    return history.id || index;
  }

  getAvatarColor(name: string): string {
    const colors = [
      '#4361ee', '#3a0ca3', '#4cc9f0', '#2ecc71', '#e74c3c',
      '#f39c12', '#9b59b6', '#1abc9c', '#34495e', '#e67e22'
    ];
    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  }

  getMembershipCardClass(membership: any): string {
    const remainingDays = this.getRemainingDays(membership.freezeEndDate);
    if (remainingDays <= 3) return 'card-critical';
    if (remainingDays <= 10) return 'card-warning';
    return 'card-safe';
  }

  getTableRowClass(membership: any): string {
    const remainingDays = this.getRemainingDays(membership.freezeEndDate);
    if (remainingDays <= 3) return 'row-critical';
    if (remainingDays <= 10) return 'row-warning';
    return 'row-safe';
  }

  getStatusBadgeClass(membership: any): string {
    const remainingDays = this.getRemainingDays(membership.freezeEndDate);
    if (remainingDays <= 3) return 'status-critical';
    if (remainingDays <= 10) return 'status-warning';
    return 'status-safe';
  }

  getStatusTextClass(membership: any): string {
    const remainingDays = this.getRemainingDays(membership.freezeEndDate);
    if (remainingDays <= 3) return 'text-danger';
    if (remainingDays <= 10) return 'text-warning';
    return 'text-success';
  }

  getStatusText(membership: any): string {
    const remainingDays = this.getRemainingDays(membership.freezeEndDate);
    if (remainingDays <= 3) return 'Kritik Durum';
    if (remainingDays <= 10) return 'Uyarı';
    return 'Güvenli';
  }

  getHistoryTypeBadgeClass(history: MembershipFreezeHistory): string {
    if (history.cancellationType === 'İptal') return 'modern-badge-danger';
    if (history.cancellationType === 'Erken Aktifleştirme') return 'modern-badge-warning';
    return 'modern-badge-success';
  }
}
