/* Frozen Memberships Component Styles - Responsive Design */

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Toggle Buttons Container */
.toggle-buttons-container {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.toggle-btn {
  position: relative;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius-lg);
  transition: all 0.3s ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 200px;
  justify-content: center;
}

.toggle-btn:hover {
  background-color: var(--primary-light);
  border-color: var(--primary);
  color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.toggle-btn.active {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.badge-count {
  background-color: rgba(255, 255, 255, 0.2);
  color: inherit;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-pill);
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 0.5rem;
}

.toggle-btn.active .badge-count {
  background-color: rgba(255, 255, 255, 0.3);
}



/* Search and Filter Inputs */
.search-input-container {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  z-index: 2;
}

.search-input {
  padding-left: 2.5rem !important;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.form-select {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light);
}

/* Membership Cards (Mobile/Tablet) */
.membership-cards-container,
.history-cards-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.membership-card,
.history-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: all 0.3s ease;
}

.membership-card:hover,
.history-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.membership-card-header,
.history-card-header {
  padding: 1rem;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.member-avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: 600;
  font-size: 1rem;
  flex-shrink: 0;
}

.member-details {
  flex: 1;
}

.member-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.2;
}

.member-phone {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.2;
}

.status-badge {
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius-md);
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.status-critical {
  background: var(--danger-light);
  color: var(--danger);
}

.status-warning {
  background: var(--warning-light);
  color: var(--warning);
}

.status-safe {
  background: var(--success-light);
  color: var(--success);
}

.history-type-badge {
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius-md);
  font-size: 0.875rem;
  font-weight: 600;
}

.membership-card-body,
.history-card-body {
  padding: 1rem;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  align-items: center;
  width: 100%;
}

.info-label {
  font-weight: 500;
  color: var(--text-secondary);
  margin-right: 0.5rem;
  min-width: 80px;
}

.info-value {
  color: var(--text-primary);
  font-weight: 500;
}

.membership-card-footer {
  padding: 1rem;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 0.5rem;
}

.membership-card-footer .modern-btn {
  flex: 1;
}

/* Desktop Table Styles */
.member-info-table {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.member-info-table .member-avatar {
  width: 40px;
  height: 40px;
  font-size: 0.875rem;
}

.member-info-table .member-details {
  flex: 1;
}

.member-info-table .member-name {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.2;
}

.member-info-table .member-phone {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.2;
}

.date-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.date-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.date-label {
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 70px;
}

.date-value {
  color: var(--text-primary);
}

.status-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.status-badge-large {
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius-md);
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-text {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.action-buttons-table {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.duration-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.duration-item {
  text-align: center;
}

/* Empty State */
.empty-state,
.empty-state-table {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-secondary);
}

.empty-state-icon {
  font-size: 4rem;
  color: var(--text-muted);
  margin-bottom: 1rem;
}

.empty-state-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.empty-state-text {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Table Row Classes */
.table-row {
  transition: all 0.3s ease;
}

.table-row:hover {
  background-color: var(--primary-light);
}

.row-critical {
  border-left: 4px solid var(--danger);
  background-color: rgba(220, 53, 69, 0.05);
}

.row-warning {
  border-left: 4px solid var(--warning);
  background-color: rgba(255, 193, 7, 0.05);
}

.row-safe {
  border-left: 4px solid var(--success);
  background-color: rgba(40, 167, 69, 0.05);
}

/* Card Classes */
.card-critical {
  border-left: 4px solid var(--danger);
  background: linear-gradient(to right, rgba(220, 53, 69, 0.05), transparent);
}

.card-warning {
  border-left: 4px solid var(--warning);
  background: linear-gradient(to right, rgba(255, 193, 7, 0.05), transparent);
}

.card-safe {
  border-left: 4px solid var(--success);
  background: linear-gradient(to right, rgba(40, 167, 69, 0.05), transparent);
}

/* Dark Mode Support */
[data-theme="dark"] .toggle-btn {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .toggle-btn:hover {
  background-color: var(--primary-light);
  border-color: var(--primary);
}

[data-theme="dark"] .membership-card,
[data-theme="dark"] .history-card {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .membership-card-header,
[data-theme="dark"] .history-card-header,
[data-theme="dark"] .membership-card-footer {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: var(--border-color);
}

[data-theme="dark"] .search-input,
[data-theme="dark"] .form-select {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .search-input:focus,
[data-theme="dark"] .form-select:focus {
  background-color: var(--bg-secondary);
  border-color: var(--primary);
}



[data-theme="dark"] .row-critical {
  background-color: rgba(244, 67, 54, 0.1);
}

[data-theme="dark"] .row-warning {
  background-color: rgba(255, 183, 77, 0.1);
}

[data-theme="dark"] .row-safe {
  background-color: rgba(76, 175, 80, 0.1);
}

[data-theme="dark"] .card-critical {
  background: linear-gradient(to right, rgba(244, 67, 54, 0.1), transparent);
}

[data-theme="dark"] .card-warning {
  background: linear-gradient(to right, rgba(255, 183, 77, 0.1), transparent);
}

[data-theme="dark"] .card-safe {
  background: linear-gradient(to right, rgba(76, 175, 80, 0.1), transparent);
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .toggle-buttons-container {
    flex-direction: column;
    align-items: center;
  }

  .toggle-btn {
    width: 100%;
    max-width: 400px;
  }
}

@media (max-width: 767.98px) {
  .toggle-btn {
    min-width: auto;
    padding: 0.875rem 1.25rem;
    font-size: 0.9rem;
  }

  .membership-card-header,
  .history-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .status-badge,
  .history-type-badge {
    align-self: flex-end;
  }

  .member-info {
    width: 100%;
  }

  .info-label {
    min-width: 90px;
    font-size: 0.875rem;
  }

  .info-value {
    font-size: 0.875rem;
  }

  .membership-card-footer {
    flex-direction: column;
    gap: 0.75rem;
  }

  .membership-card-footer .modern-btn {
    width: 100%;
  }
}

@media (max-width: 575.98px) {
  .toggle-btn {
    min-width: auto;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .member-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 0.5rem;
  }

  .member-details {
    text-align: center;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .info-label {
    min-width: auto;
    font-weight: 600;
  }
}

/* Animation Classes */
.slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stagger-animation .membership-card:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation .membership-card:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation .membership-card:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation .membership-card:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation .membership-card:nth-child(5) { animation-delay: 0.5s; }
